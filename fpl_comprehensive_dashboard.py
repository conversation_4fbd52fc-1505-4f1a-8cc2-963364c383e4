"""
Fantasy Premier League End of Season Review Dashboard - COMPREHENSIVE VERSION
Modern UI with complete analysis including chips, transfers, captaincy, and awards
"""

import requests
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import dash
from dash import dcc, html, Input, Output
import time
import json
import os

# Configuration
LEAGUE_ID = 623787
BOOTSTRAP_STATIC_URL = "https://fantasy.premierleague.com/api/bootstrap-static/"

def load_historical_seasons_summary():
    """Load summary of all available historical seasons"""
    try:
        if os.path.exists('historical_seasons_summary.json'):
            with open('historical_seasons_summary.json', 'r') as f:
                data = json.load(f)
                print(f"✅ Loaded historical data for {len(data['available_seasons'])} seasons")
                return data
    except Exception as e:
        print(f"⚠️ Could not load historical seasons summary: {e}")

    return None

def load_season_data(season_year):
    """Load data for a specific season year"""
    try:
        # Try to load from summary first
        summary = load_historical_seasons_summary()
        if summary and str(season_year) in summary['seasons_data']:
            return summary['seasons_data'][str(season_year)]

        # Try to load individual season file
        season_file = f'historical_season_{season_year}_stats.json'
        if os.path.exists(season_file):
            with open(season_file, 'r') as f:
                data = json.load(f)
                print(f"✅ Loaded {season_year} season data")
                return data

        # Fallback for old method (2022 season)
        if season_year == 2022 and os.path.exists('historical_season_stats.json'):
            with open('historical_season_stats.json', 'r') as f:
                data = json.load(f)
                print("✅ Loaded 2022 season data (legacy)")
                return data

    except Exception as e:
        print(f"⚠️ Could not load {season_year} season data: {e}")

    # Return default data if no files exist
    print(f"⚠️ No data found for {season_year}, using default comparison data")
    return {
        'champion_name': 'Historical Data',
        'champion_points': 2250,
        'average_points': 2100,
        'points_range': 180,
        'std_points': 85,
        'total_managers': 4,
        'competition_level': 'Very Close',
        'season_name': f'{season_year}/{str(season_year+1)[2:]}',
        'season_year': season_year
    }

# Modern color palette
COLORS = {
    'primary': '#37003c',      # FPL Purple
    'secondary': '#00ff87',    # FPL Green
    'accent': '#e90052',       # FPL Pink
    'background': '#f8fafc',   # Light gray
    'card': '#ffffff',         # White
    'text': '#1e293b',         # Dark gray
    'muted': '#64748b',        # Medium gray
    'success': '#10b981',      # Green
    'warning': '#f59e0b',      # Orange
    'danger': '#ef4444'        # Red
}

class ComprehensiveFPLAnalyzer:
    def __init__(self, league_id):
        self.league_id = league_id
        self.league_data = None
        self.gameweek_data = None
        self.manager_histories = {}
        self.manager_picks = {}
        self.bootstrap_data = None
        self.league_info = {}
        self.players_data = None
        
    def fetch_bootstrap_data(self):
        """Fetch general FPL data including gameweeks and players"""
        print("📊 Fetching FPL bootstrap data...")
        response = requests.get(BOOTSTRAP_STATIC_URL)
        self.bootstrap_data = response.json()
        
        # Extract gameweek information
        events = self.bootstrap_data['events']
        self.gameweek_data = pd.DataFrame([{
            'id': event['id'],
            'name': event['name'],
            'average_score': event['average_entry_score'],
            'highest_score': event['highest_score'],
            'finished': event['finished'],
            'deadline_time': event['deadline_time']
        } for event in events if event['finished']])
        
        # Extract players data
        self.players_data = pd.DataFrame(self.bootstrap_data['elements'])
        
        print(f"✅ Found {len(self.gameweek_data)} completed gameweeks")
        print(f"✅ Found {len(self.players_data)} players")
        
    def fetch_league_standings(self):
        """Fetch league standings with pagination"""
        print(f"🏆 Fetching league {self.league_id} standings...")
        all_results = []
        page = 1
        
        while True:
            url = f"https://fantasy.premierleague.com/api/leagues-classic/{self.league_id}/standings/?page_standings={page}"
            response = requests.get(url)
            data = response.json()
            
            if page == 1:
                self.league_info = data['league']
                
            results = data['standings']['results']
            all_results.extend(results)
            
            if data['standings']['has_next']:
                page += 1
                time.sleep(0.5)
            else:
                break
                
        self.league_data = pd.DataFrame(all_results)
        print(f"✅ Found {len(self.league_data)} managers in league")
        
    def fetch_manager_history(self, entry_id):
        """Fetch gameweek history for a specific manager"""
        if entry_id in self.manager_histories:
            return self.manager_histories[entry_id]
            
        print(f"📈 Fetching history for manager {entry_id}...")
        url = f"https://fantasy.premierleague.com/api/entry/{entry_id}/history/"
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            history_df = pd.DataFrame(data['current'])
            self.manager_histories[entry_id] = history_df
            time.sleep(0.3)
            return history_df
        else:
            print(f"❌ Failed to fetch history for {entry_id}")
            return pd.DataFrame()
    
    def fetch_manager_picks(self, entry_id, gameweek):
        """Fetch manager picks for a specific gameweek"""
        url = f"https://fantasy.premierleague.com/api/entry/{entry_id}/event/{gameweek}/picks/"
        try:
            response = requests.get(url)
            if response.status_code == 200:
                return response.json()
            time.sleep(0.2)  # Rate limiting
        except:
            pass
        return None
    
    def get_all_manager_histories(self):
        """Fetch histories for all managers in the league"""
        print("📊 Fetching all manager histories...")
        for _, manager in self.league_data.iterrows():
            self.fetch_manager_history(manager['entry'])
        print("✅ All manager histories fetched")
        
    def fetch_sample_picks_data(self):
        """Fetch sample picks data for analysis (limited to avoid API overload)"""
        print("🎯 Fetching sample picks data...")
        sample_gameweeks = [1, 10, 20, 30, 38]  # Sample gameweeks
        
        for _, manager in self.league_data.head(2).iterrows():  # Sample managers
            manager_picks = {}
            for gw in sample_gameweeks:
                picks = self.fetch_manager_picks(manager['entry'], gw)
                if picks:
                    manager_picks[gw] = picks
            self.manager_picks[manager['entry']] = manager_picks
        
        print("✅ Sample picks data fetched")
        
    def calculate_comprehensive_stats(self):
        """Calculate comprehensive league statistics"""
        if self.league_data is None:
            return {}
            
        champion = self.league_data.iloc[0]
        runner_up = self.league_data.iloc[1] if len(self.league_data) > 1 else None
        
        # Basic stats
        stats = {
            'total_managers': len(self.league_data),
            'champion_name': champion['entry_name'],  # Changed to team name
            'champion_team': champion['entry_name'],
            'champion_points': champion['total'],
            'average_points': self.league_data['total'].mean(),
            'highest_points': self.league_data['total'].max(),
            'lowest_points': self.league_data['total'].min(),
            'points_range': self.league_data['total'].max() - self.league_data['total'].min(),
            'median_points': self.league_data['total'].median(),
            'std_points': self.league_data['total'].std(),
        }

        if runner_up is not None:
            stats['winning_margin'] = champion['total'] - runner_up['total']
            stats['runner_up_name'] = runner_up['entry_name']  # Changed to team name
        
        # Advanced analysis
        all_manager_stats = []
        
        for _, manager in self.league_data.iterrows():
            history = self.manager_histories.get(manager['entry'])
            if history is not None and not history.empty:
                manager_stat = self.analyze_manager_performance(manager, history)
                all_manager_stats.append(manager_stat)
        
        stats['manager_stats'] = pd.DataFrame(all_manager_stats)
        
        return stats
    
    def analyze_manager_performance(self, manager, history):
        """Analyze individual manager performance"""
        if history.empty or 'points' not in history.columns:
            return {
                'entry_id': manager['entry'],
                'manager_name': manager['player_name'],
                'team_name': manager['entry_name'],
                'final_rank': manager['rank'],
                'total_points': 0,
                'avg_points': 0,
                'consistency': 0,
                'best_gw': 0,
                'worst_gw': 0,
                'total_transfers': 0,
                'transfer_cost': 0,
                'bench_points': 0,
                'start_rank': 0,
                'end_rank': 0,
                'rank_improvement': 0,
                'best_rank': 0,
                'worst_rank': 0,
                'recent_form': 0,
            }

        try:
            total_points = history['points'].sum()
            avg_points = history['points'].mean()
            consistency = history['points'].std()
            best_gw = history['points'].max()
            worst_gw = history['points'].min()

            # Transfer analysis
            total_transfers = history['event_transfers'].sum() if 'event_transfers' in history.columns else 0
            transfer_cost = history['event_transfers_cost'].sum() if 'event_transfers_cost' in history.columns else 0

            # Bench analysis
            bench_points = history['points_on_bench'].sum() if 'points_on_bench' in history.columns else 0

            # Rank progression
            if 'overall_rank' in history.columns and len(history) > 0:
                start_rank = history.iloc[0]['overall_rank']
                end_rank = history.iloc[-1]['overall_rank']
                rank_improvement = start_rank - end_rank  # Positive is improvement
                best_rank = history['overall_rank'].min()
                worst_rank = history['overall_rank'].max()
            else:
                start_rank = end_rank = rank_improvement = best_rank = worst_rank = 0

            # Form analysis
            recent_form = history.tail(5)['points'].mean() if len(history) >= 5 else avg_points

            return {
                'entry_id': manager['entry'],
                'manager_name': manager['entry_name'],  # Changed to team name
                'team_name': manager['entry_name'],
                'final_rank': manager['rank'],
                'total_points': total_points,
                'avg_points': avg_points,
                'consistency': consistency,
                'best_gw': best_gw,
                'worst_gw': worst_gw,
                'total_transfers': total_transfers,
                'transfer_cost': transfer_cost,
                'bench_points': bench_points,
                'start_rank': start_rank,
                'end_rank': end_rank,
                'rank_improvement': rank_improvement,
                'best_rank': best_rank,
                'worst_rank': worst_rank,
                'recent_form': recent_form,
            }
        except Exception as e:
            print(f"❌ Error analyzing manager {manager['entry_name']}: {e}")  # Changed to team name
            return {
                'entry_id': manager['entry'],
                'manager_name': manager['entry_name'],  # Changed to team name
                'team_name': manager['entry_name'],
                'final_rank': manager['rank'],
                'total_points': 0,
                'avg_points': 0,
                'consistency': 0,
                'best_gw': 0,
                'worst_gw': 0,
                'total_transfers': 0,
                'transfer_cost': 0,
                'bench_points': 0,
                'start_rank': 0,
                'end_rank': 0,
                'rank_improvement': 0,
                'best_rank': 0,
                'worst_rank': 0,
                'recent_form': 0,
            }

# Initialize the analyzer
print("🚀 Starting comprehensive FPL data collection...")
analyzer = ComprehensiveFPLAnalyzer(LEAGUE_ID)
analyzer.fetch_bootstrap_data()
analyzer.fetch_league_standings()
analyzer.get_all_manager_histories()
analyzer.fetch_sample_picks_data()

# Calculate comprehensive stats
league_stats = analyzer.calculate_comprehensive_stats()

print("✅ Comprehensive data collection complete!")
print(f"   🏆 Champion: {league_stats['champion_name']} ({league_stats['champion_points']:,} points)")
print(f"   👥 Total Managers: {league_stats['total_managers']}")
print(f"   📈 Average Points: {league_stats['average_points']:.0f}")
print(f"   📊 Points Range: {league_stats['points_range']:,}")

# Initialize Dash app with assets folder
app = dash.Dash(__name__, suppress_callback_exceptions=True, assets_folder='assets')
app.title = f"FPL League {LEAGUE_ID} - Comprehensive End of Season Review"

# Modern CSS styling
app.index_string = '''
<!DOCTYPE html>
<html>
    <head>
        {%metas%}
        <title>{%title%}</title>
        {%favicon%}
        {%css%}
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <style>
            * {
                margin: 0;
                padding: 0;
                box-sizing: border-box;
            }
            body {
                font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                min-height: 100vh;
            }
            .main-container {
                background: white;
                margin: 20px;
                border-radius: 20px;
                box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
                overflow: hidden;
            }
            .header-section {
                background: linear-gradient(135deg, #37003c 0%, #e90052 100%);
                color: white;
                padding: 40px;
                text-align: center;
            }
            .stat-card {
                background: white;
                border-radius: 16px;
                padding: 24px;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                border: 1px solid #e2e8f0;
                transition: all 0.3s ease;
                margin: 12px;
            }
            .stat-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
            }
            .metric-value {
                font-size: 2.5rem;
                font-weight: 700;
                color: #37003c;
                margin-bottom: 8px;
                word-wrap: break-word;
                overflow-wrap: break-word;
                hyphens: auto;
                max-width: 100%;
                text-align: center;
                line-height: 1.1;
            }
            .metric-label {
                font-size: 0.875rem;
                color: #64748b;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                font-weight: 500;
            }
            .section-title {
                font-size: 1.875rem;
                font-weight: 600;
                color: #1e293b;
                margin-bottom: 24px;
                text-align: center;
            }
            .award-card {
                background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
                border-radius: 16px;
                padding: 20px;
                text-align: center;
                border: 2px solid #e2e8f0;
                transition: all 0.3s ease;
                margin: 12px;
            }
            .award-card:hover {
                border-color: #37003c;
                transform: translateY(-2px);
            }
            .award-icon {
                font-size: 3rem;
                margin-bottom: 12px;
            }
            .award-winner {
                font-size: 1.25rem;
                font-weight: 600;
                color: #37003c;
                margin-bottom: 8px;
                word-wrap: break-word;
                overflow-wrap: break-word;
                hyphens: auto;
                max-width: 100%;
                text-align: center;
                line-height: 1.2;
            }
            .award-title {
                font-size: 0.875rem;
                color: #64748b;
                text-transform: uppercase;
                letter-spacing: 0.05em;
                font-weight: 500;
                margin-bottom: 8px;
            }
            .award-detail {
                font-size: 0.875rem;
                color: #64748b;
            }
        </style>
    </head>
    <body>
        {%app_entry%}
        <footer>
            {%config%}
            {%scripts%}
            {%renderer%}
        </footer>
    </body>
</html>
'''

# Define the comprehensive layout
app.layout = html.Div([
    html.Div([
        # Header Section
        html.Div([
            html.H1("🏆 Fantasy Premier League",
                    style={'fontSize': '3rem', 'fontWeight': '700', 'marginBottom': '8px'}),
            html.H2("Comprehensive End of Season Review",
                    style={'fontSize': '1.5rem', 'fontWeight': '400', 'opacity': '0.9'}),
            html.P(f"League {LEAGUE_ID} - {analyzer.league_info.get('name', 'Private League')}",
                   style={'fontSize': '1.125rem', 'marginTop': '16px', 'opacity': '0.8'}),
        ], className='header-section'),

        # Key Metrics Section
        html.Div([
            html.H2("📊 Season Overview", className='section-title'),
            html.Div([
                # Champion Card
                html.Div([
                    html.Div("🏆", className='award-icon'),
                    html.Div(league_stats['champion_name'], className='metric-value'),
                    html.Div("League Champion", className='metric-label'),
                    html.Div(f"{league_stats['champion_points']:,} points",
                            style={'fontSize': '1.25rem', 'fontWeight': '600', 'color': COLORS['secondary'], 'marginTop': '8px'}),
                    html.Div(f"Margin: {league_stats.get('winning_margin', 0)} points",
                            style={'fontSize': '0.875rem', 'color': COLORS['muted'], 'marginTop': '4px'}),
                ], className='stat-card'),

                # Competition Closeness
                html.Div([
                    html.Div("📈", className='award-icon'),
                    html.Div(f"{league_stats['points_range']:,}", className='metric-value'),
                    html.Div("Points Range", className='metric-label'),
                    html.Div(f"Std Dev: {league_stats['std_points']:.0f}",
                            style={'fontSize': '1rem', 'color': COLORS['muted'], 'marginTop': '8px'}),
                ], className='stat-card'),

                # League Stats
                html.Div([
                    html.Div("👥", className='award-icon'),
                    html.Div(f"{league_stats['total_managers']}", className='metric-value'),
                    html.Div("Total Managers", className='metric-label'),
                    html.Div(f"Avg: {league_stats['average_points']:.0f} pts",
                            style={'fontSize': '1rem', 'color': COLORS['muted'], 'marginTop': '8px'}),
                ], className='stat-card'),

                # Highest Score
                html.Div([
                    html.Div("🎯", className='award-icon'),
                    html.Div(f"{league_stats['highest_points']:,}", className='metric-value'),
                    html.Div("Highest Score", className='metric-label'),
                    html.Div(f"Lowest: {league_stats['lowest_points']:,}",
                            style={'fontSize': '1rem', 'color': COLORS['muted'], 'marginTop': '8px'}),
                ], className='stat-card'),

            ], style={'display': 'grid', 'gridTemplateColumns': 'repeat(auto-fit, minmax(250px, 1fr))',
                     'gap': '24px', 'marginBottom': '48px'}),
        ], style={'padding': '40px'}),

        # Navigation Tabs
        html.Div([
            dcc.Tabs(id='main-tabs', value='overview', children=[
                dcc.Tab(label='📊 League Overview', value='overview'),
                dcc.Tab(label='📈 Performance Analysis', value='performance'),
                dcc.Tab(label='🏆 Awards & Records', value='awards'),
                dcc.Tab(label='🔄 Transfers & Strategy', value='transfers'),
                dcc.Tab(label='👤 Manager Deep Dive', value='manager'),
                dcc.Tab(label='⚖️ Manager Comparison', value='comparison'),
                dcc.Tab(label='📖 Season Story', value='story'),  # Changed from analytics
                dcc.Tab(label='📅 Season Comparison', value='season_comparison'),  # New tab
                dcc.Tab(label='🎁 Surprise', value='surprise'),  # Surprise tab
            ], style={'marginBottom': '32px'}),

            # Tab Content
            html.Div(id='tab-content'),

        ], style={'padding': '0 40px 40px 40px'}),

    ], className='main-container'),
], style={'background': 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)', 'minHeight': '100vh', 'padding': '20px'})

# Chart creation functions
def create_league_table():
    """Create modern final league table"""
    if analyzer.league_data is None or analyzer.league_data.empty:
        fig = go.Figure()
        fig.add_annotation(text="No league data available",
                          xref="paper", yref="paper", x=0.5, y=0.5, showarrow=False)
        return fig

    try:
        fig = go.Figure(data=[go.Table(
            header=dict(
                values=['Rank', 'Manager', 'Team Name', 'Total Points', 'Overall Rank'],
                fill_color=COLORS['primary'],
                font=dict(color='white', size=14, family='Inter'),
                align='center',
                height=40
            ),
            cells=dict(
                values=[
                    analyzer.league_data['rank'],
                    analyzer.league_data['player_name'],  # Manager name column
                    analyzer.league_data['entry_name'],   # Team name column
                    [f"{points:,}" for points in analyzer.league_data['total']],
                    [f"{rank:,}" for rank in analyzer.league_data.get('last_rank', analyzer.league_data['rank'])]
                ],
                fill_color=[['#ffffff' if i % 2 == 0 else '#f8fafc' for i in range(len(analyzer.league_data))]],
                align='center',
                font=dict(size=12, family='Inter'),
                height=35
            )
        )])

        fig.update_layout(
            title=dict(
                text="Final League Standings",
                font=dict(size=24, family='Inter', color=COLORS['text']),
                x=0.5
            ),
            height=400,
            margin=dict(l=0, r=0, t=60, b=0),
            paper_bgcolor='white',
            plot_bgcolor='white'
        )
        return fig
    except Exception as e:
        print(f"❌ Error creating league table: {e}")
        fig = go.Figure()
        fig.add_annotation(text=f"Error creating table: {str(e)}",
                          xref="paper", yref="paper", x=0.5, y=0.5, showarrow=False)
        return fig

def create_league_difficulty_analysis():
    """Create league difficulty analysis compared to global FPL standards"""
    if analyzer.league_data is None or analyzer.league_data.empty or analyzer.gameweek_data is None:
        fig = go.Figure()
        fig.add_annotation(text="No data available for analysis",
                          xref="paper", yref="paper", x=0.5, y=0.5, showarrow=False)
        return fig

    try:
        fig = go.Figure()

        # Calculate league statistics
        league_avg = analyzer.league_data['total'].mean()
        league_highest = analyzer.league_data['total'].max()
        league_lowest = analyzer.league_data['total'].min()

        # Calculate global season averages from gameweek data
        global_season_avg = analyzer.gameweek_data['average_score'].sum()
        global_highest_possible = analyzer.gameweek_data['highest_score'].sum()

        # Estimate global ranges (based on typical FPL distributions)
        global_top_1_percent = global_highest_possible * 0.85  # Estimate for top 1%
        global_top_10_percent = global_season_avg * 1.15      # Estimate for top 10%
        global_bottom_10_percent = global_season_avg * 0.85   # Estimate for bottom 10%

        # Create comparison bars
        categories = ['Global Bottom 10%', 'Global Average', 'Global Top 10%', 'Global Top 1%', 'League Average', 'League Best', 'League Worst']
        values = [global_bottom_10_percent, global_season_avg, global_top_10_percent, global_top_1_percent,
                 league_avg, league_highest, league_lowest]
        colors = ['#ef4444', '#64748b', '#10b981', '#00ff87', '#37003c', '#e90052', '#f59e0b']

        fig.add_trace(go.Bar(
            x=categories,
            y=values,
            marker_color=colors,
            text=[f"{val:,.0f}" for val in values],
            textposition='auto',
            name='Points Comparison'
        ))

        # Add league performance indicators
        league_quality = "Elite" if league_avg > global_top_10_percent else "Above Average" if league_avg > global_season_avg else "Below Average"
        champion_quality = "World Class" if league_highest > global_top_1_percent else "Elite" if league_highest > global_top_10_percent else "Good"

        fig.update_layout(
            title=dict(
                text=f"League Difficulty Analysis vs Global Standards<br><sub>League Quality: {league_quality} | Champion Level: {champion_quality}</sub>",
                font=dict(size=20, family='Inter', color=COLORS['text']),
                x=0.5
            ),
            xaxis=dict(title=dict(text="Performance Categories", font=dict(family='Inter'))),
            yaxis=dict(title=dict(text="Total Season Points", font=dict(family='Inter'))),
            height=400,
            paper_bgcolor='white',
            plot_bgcolor='white',
            showlegend=False
        )
        return fig
    except Exception as e:
        print(f"❌ Error creating league difficulty analysis: {e}")
        fig = go.Figure()
        fig.add_annotation(text=f"Error creating chart: {str(e)}",
                          xref="paper", yref="paper", x=0.5, y=0.5, showarrow=False)
        return fig

def create_points_progression():
    """Create cumulative points progression"""
    fig = go.Figure()
    colors = px.colors.qualitative.Set3

    for i, (_, manager) in enumerate(analyzer.league_data.iterrows()):
        history = analyzer.manager_histories.get(manager['entry'])

        if history is not None and not history.empty and 'points' in history.columns:
            cumulative_points = history['points'].cumsum()

            fig.add_trace(go.Scatter(
                x=history['event'],
                y=cumulative_points,
                mode='lines+markers',
                name=manager['entry_name'],  # Changed to team name
                line=dict(width=3, color=colors[i % len(colors)]),
                marker=dict(size=6),
                hovertemplate=f"<b>{manager['entry_name']}</b><br>" +  # Changed to team name
                             "Gameweek: %{x}<br>" +
                             "Total Points: %{y:,}<br>" +
                             "<extra></extra>"
            ))

    fig.update_layout(
        title=dict(
            text="Cumulative Points Progression Throughout Season",
            font=dict(size=20, family='Inter', color=COLORS['text']),
            x=0.5
        ),
        xaxis=dict(title=dict(text="Gameweek", font=dict(family='Inter')), gridcolor='#e2e8f0', showgrid=True),
        yaxis=dict(title=dict(text="Cumulative Points", font=dict(family='Inter')), gridcolor='#e2e8f0', showgrid=True),
        hovermode='x unified',
        height=500,
        paper_bgcolor='white',
        plot_bgcolor='white',
        legend=dict(orientation="v", yanchor="top", y=1, xanchor="left", x=1.02, font=dict(family='Inter'))
    )
    return fig

def create_rank_progression():
    """Create overall rank progression"""
    fig = go.Figure()
    colors = px.colors.qualitative.Set3

    for i, (_, manager) in enumerate(analyzer.league_data.iterrows()):
        history = analyzer.manager_histories.get(manager['entry'])
        if history is not None and not history.empty and 'overall_rank' in history.columns:
            fig.add_trace(go.Scatter(
                x=history['event'],
                y=history['overall_rank'],
                mode='lines+markers',
                name=manager['entry_name'],  # Changed to team name
                line=dict(width=3, color=colors[i % len(colors)]),
                marker=dict(size=6),
                hovertemplate=f"<b>{manager['entry_name']}</b><br>" +  # Changed to team name
                             "Gameweek: %{x}<br>" +
                             "Overall Rank: %{y:,}<br>" +
                             "<extra></extra>"
            ))

    fig.update_layout(
        title=dict(
            text="Overall FPL Rank Progression",
            font=dict(size=20, family='Inter', color=COLORS['text']),
            x=0.5
        ),
        xaxis=dict(title=dict(text="Gameweek", font=dict(family='Inter')), gridcolor='#e2e8f0', showgrid=True),
        yaxis=dict(title=dict(text="Overall Rank", font=dict(family='Inter')), autorange='reversed', gridcolor='#e2e8f0', showgrid=True),
        hovermode='x unified',
        height=500,
        paper_bgcolor='white',
        plot_bgcolor='white',
        legend=dict(orientation="v", yanchor="top", y=1, xanchor="left", x=1.02, font=dict(family='Inter'))
    )
    return fig

# Main tab content callback
@app.callback(
    Output('tab-content', 'children'),
    Input('main-tabs', 'value')
)
def update_tab_content(active_tab):
    if active_tab == 'overview':
        return create_overview_tab()
    elif active_tab == 'performance':
        return create_performance_tab()
    elif active_tab == 'awards':
        return create_awards_tab()
    elif active_tab == 'transfers':
        return create_transfers_tab()
    elif active_tab == 'manager':
        return create_manager_tab()
    elif active_tab == 'comparison':
        return create_comparison_tab()
    elif active_tab == 'story':
        return create_season_story_tab()
    elif active_tab == 'season_comparison':
        return create_season_comparison_tab()
    elif active_tab == 'surprise':
        return create_surprise_tab()
    return html.Div("Select a tab")

def create_overview_tab():
    """League Overview & Standings"""
    return html.Div([
        # Header Image
        html.Div([
            html.Img(
                src="/assets/home_header.jpg",
                style={
                    'width': '100%',
                    'maxWidth': '1200px',
                    'height': 'auto',
                    'borderRadius': '12px',
                    'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                    'marginBottom': '32px'
                }
            )
        ], style={'textAlign': 'center', 'marginBottom': '32px'}),

        # Final League Table
        html.Div([
            dcc.Graph(figure=create_league_table()),
        ], style={'marginBottom': '32px'}),

        # League Difficulty Analysis
        html.Div([
            html.Div([
                dcc.Graph(figure=create_league_difficulty_analysis()),
            ], style={'width': '60%', 'display': 'inline-block'}),

            html.Div([
                html.H4("🌍 Global Performance Analysis", style={'color': COLORS['text'], 'marginBottom': '16px'}),
                html.Div([
                    html.P(f"🏆 League Champion: {', '.join(analyzer.league_data.head(1)['entry_name'].tolist())}",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P(f"📈 League Average: {league_stats['average_points']:.0f} points",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P(f"🌍 Global Average: {analyzer.gameweek_data['average_score'].sum():.0f} points",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P(f"📊 League vs Global: {league_stats['average_points'] - analyzer.gameweek_data['average_score'].sum():+.0f} points",
                           style={'margin': '8px 0', 'fontSize': '1rem', 'fontWeight': 'bold',
                                 'color': COLORS['success'] if league_stats['average_points'] > analyzer.gameweek_data['average_score'].sum() else COLORS['danger']}),
                    html.P(f"🎯 League Quality: {'Above Average' if league_stats['average_points'] > analyzer.gameweek_data['average_score'].sum() else 'Below Average'}",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P(f"🏅 Competition Level: {'High' if league_stats['std_points'] < 100 else 'Moderate'}",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                ], style={'backgroundColor': COLORS['background'], 'padding': '20px', 'borderRadius': '12px'})
            ], style={'width': '38%', 'display': 'inline-block', 'verticalAlign': 'top', 'paddingLeft': '20px'}),
        ]),
    ])

def create_performance_tab():
    """Performance Analysis"""
    return html.Div([
        html.H3("📈 Season Performance Analysis", className='section-title'),

        # Points progression
        html.Div([
            dcc.Graph(figure=create_points_progression()),
        ], style={'marginBottom': '32px'}),

        # Rank progression
        html.Div([
            dcc.Graph(figure=create_rank_progression()),
        ]),
    ])

def create_awards_tab():
    """Awards & Records"""
    if 'manager_stats' not in league_stats or league_stats['manager_stats'].empty:
        return html.Div("No advanced statistics available")

    manager_stats = league_stats['manager_stats']

    # Calculate additional efficiency metrics for awards
    try:
        manager_stats['transfer_efficiency'] = manager_stats['total_points'] / (manager_stats['total_transfers'] + 1)  # +1 to avoid division by zero
        manager_stats['bench_efficiency'] = 100 - (manager_stats['bench_points'] / manager_stats['total_points'] * 100)
    except (KeyError, ValueError, ZeroDivisionError):
        pass

    # Calculate awards with better error handling
    try:
        most_consistent = manager_stats.loc[manager_stats['consistency'].idxmin()] if len(manager_stats) > 0 and not manager_stats['consistency'].isna().all() else None
    except (KeyError, ValueError):
        most_consistent = None

    try:
        best_form = manager_stats.loc[manager_stats['recent_form'].idxmax()] if len(manager_stats) > 0 and not manager_stats['recent_form'].isna().all() else None
    except (KeyError, ValueError):
        best_form = None

    try:
        transfer_king = manager_stats.loc[manager_stats['total_transfers'].idxmax()] if len(manager_stats) > 0 and not manager_stats['total_transfers'].isna().all() else None
    except (KeyError, ValueError):
        transfer_king = None

    try:
        bench_warmer = manager_stats.loc[manager_stats['bench_points'].idxmax()] if len(manager_stats) > 0 and not manager_stats['bench_points'].isna().all() else None
    except (KeyError, ValueError):
        bench_warmer = None

    try:
        best_gw = manager_stats.loc[manager_stats['best_gw'].idxmax()] if len(manager_stats) > 0 and not manager_stats['best_gw'].isna().all() else None
    except (KeyError, ValueError):
        best_gw = None



    return html.Div([
        html.H3("🏆 Season Awards & Records", className='section-title'),

        # Awards grid
        html.Div([
            # Most Consistent
            html.Div([
                html.Div("🎯", className='award-icon'),
                html.Div(most_consistent['manager_name'] if most_consistent is not None else 'N/A', className='award-winner'),
                html.Div("Most Consistent Manager", className='award-title'),
                html.Div(f"Std Dev: {most_consistent['consistency']:.1f}" if most_consistent is not None else 'N/A', className='award-detail'),
            ], className='award-card'),

            # Best Recent Form
            html.Div([
                html.Div("🔥", className='award-icon'),
                html.Div(best_form['manager_name'] if best_form is not None else 'N/A', className='award-winner'),
                html.Div("Best Recent Form", className='award-title'),
                html.Div(f"Last 5 GWs: {best_form['recent_form']:.1f} avg" if best_form is not None else 'N/A', className='award-detail'),
            ], className='award-card'),

            # Transfer King
            html.Div([
                html.Div("🔄", className='award-icon'),
                html.Div(transfer_king['manager_name'] if transfer_king is not None else 'N/A', className='award-winner'),
                html.Div("Transfer King", className='award-title'),
                html.Div(f"{transfer_king['total_transfers']} transfers" if transfer_king is not None else 'N/A', className='award-detail'),
            ], className='award-card'),

            # Bench Points King
            html.Div([
                html.Div("🪑", className='award-icon'),
                html.Div(bench_warmer['manager_name'] if bench_warmer is not None else 'N/A', className='award-winner'),
                html.Div("Bench Points King", className='award-title'),
                html.Div(f"{bench_warmer['bench_points']} points left" if bench_warmer is not None else 'N/A', className='award-detail'),
            ], className='award-card'),

            # Best Single Gameweek
            html.Div([
                html.Div("⚡", className='award-icon'),
                html.Div(best_gw['manager_name'] if best_gw is not None else 'N/A', className='award-winner'),
                html.Div("Best Single Gameweek", className='award-title'),
                html.Div(f"{best_gw['best_gw']} points" if best_gw is not None else 'N/A', className='award-detail'),
            ], className='award-card'),

            # Transfer Efficiency King
            html.Div([
                html.Div("🎯", className='award-icon'),
                html.Div(manager_stats.loc[manager_stats['transfer_efficiency'].idxmax(), 'manager_name'] if 'transfer_efficiency' in manager_stats.columns and not manager_stats['transfer_efficiency'].isna().all() else 'N/A', className='award-winner'),
                html.Div("Transfer Efficiency King", className='award-title'),
                html.Div(f"{manager_stats['transfer_efficiency'].max():.1f} pts/transfer" if 'transfer_efficiency' in manager_stats.columns else 'N/A', className='award-detail'),
            ], className='award-card'),

            # Squad Rotation Master
            html.Div([
                html.Div("🔄", className='award-icon'),
                html.Div(manager_stats.loc[manager_stats['bench_efficiency'].idxmax(), 'manager_name'] if 'bench_efficiency' in manager_stats.columns and not manager_stats['bench_efficiency'].isna().all() else 'N/A', className='award-winner'),
                html.Div("Squad Rotation Master", className='award-title'),
                html.Div(f"{manager_stats['bench_efficiency'].max():.1f}% efficiency" if 'bench_efficiency' in manager_stats.columns else 'N/A', className='award-detail'),
            ], className='award-card'),

            # Steady Eddie Award
            html.Div([
                html.Div("🛡️", className='award-icon'),
                html.Div(manager_stats.loc[manager_stats['worst_gw'].idxmax(), 'manager_name'] if 'worst_gw' in manager_stats.columns and not manager_stats['worst_gw'].isna().all() else 'N/A', className='award-winner'),
                html.Div("Steady Eddie Award", className='award-title'),
                html.Div(f"Best worst GW: {manager_stats['worst_gw'].max()}" if 'worst_gw' in manager_stats.columns else 'N/A', className='award-detail'),
            ], className='award-card'),

            # High Roller Award
            html.Div([
                html.Div("🎰", className='award-icon'),
                html.Div(manager_stats.loc[manager_stats['consistency'].idxmax(), 'manager_name'] if 'consistency' in manager_stats.columns and not manager_stats['consistency'].isna().all() else 'N/A', className='award-winner'),
                html.Div("High Roller Award", className='award-title'),
                html.Div(f"Most volatile: {manager_stats['consistency'].max():.1f} std dev" if 'consistency' in manager_stats.columns else 'N/A', className='award-detail'),
            ], className='award-card'),

            # Set & Forget Champion
            html.Div([
                html.Div("🏛️", className='award-icon'),
                html.Div(manager_stats.loc[manager_stats['total_transfers'].idxmin(), 'manager_name'] if 'total_transfers' in manager_stats.columns and not manager_stats['total_transfers'].isna().all() else 'N/A', className='award-winner'),
                html.Div("Set & Forget Champion", className='award-title'),
                html.Div(f"Only {manager_stats['total_transfers'].min()} transfers" if 'total_transfers' in manager_stats.columns else 'N/A', className='award-detail'),
            ], className='award-card'),



        ], style={'display': 'grid', 'gridTemplateColumns': 'repeat(auto-fit, minmax(280px, 1fr))',
                 'gap': '24px', 'marginBottom': '32px'}),
    ])

def create_transfers_tab():
    """Transfers & Strategy Analysis"""
    if 'manager_stats' not in league_stats or league_stats['manager_stats'].empty:
        return html.Div("No transfer data available")

    manager_stats = league_stats['manager_stats']

    # Safe calculations with error handling
    try:
        avg_transfers = manager_stats['total_transfers'].mean() if 'total_transfers' in manager_stats.columns else 0
        most_active_idx = manager_stats['total_transfers'].idxmax() if 'total_transfers' in manager_stats.columns and not manager_stats['total_transfers'].isna().all() else None
        most_active_name = manager_stats.loc[most_active_idx, 'manager_name'] if most_active_idx is not None else "N/A"
        max_transfers = manager_stats['total_transfers'].max() if 'total_transfers' in manager_stats.columns else 0
        total_transfer_cost = manager_stats['transfer_cost'].sum() if 'transfer_cost' in manager_stats.columns else 0
        total_bench_points = manager_stats['bench_points'].sum() if 'bench_points' in manager_stats.columns else 0

        # Calculate transfer efficiency metrics
        least_active_idx = manager_stats['total_transfers'].idxmin() if 'total_transfers' in manager_stats.columns and not manager_stats['total_transfers'].isna().all() else None
        least_active_name = manager_stats.loc[least_active_idx, 'manager_name'] if least_active_idx is not None else "N/A"
        min_transfers = manager_stats['total_transfers'].min() if 'total_transfers' in manager_stats.columns else 0

        # Calculate transfer efficiency (points per transfer)
        manager_stats['transfer_efficiency'] = manager_stats['total_points'] / (manager_stats['total_transfers'] + 1)  # +1 to avoid division by zero
        most_efficient_idx = manager_stats['transfer_efficiency'].idxmax() if 'transfer_efficiency' in manager_stats.columns and not manager_stats['transfer_efficiency'].isna().all() else None
        most_efficient_name = manager_stats.loc[most_efficient_idx, 'manager_name'] if most_efficient_idx is not None else "N/A"
        best_efficiency = manager_stats['transfer_efficiency'].max() if 'transfer_efficiency' in manager_stats.columns else 0

        # Calculate bench efficiency
        manager_stats['bench_efficiency'] = 100 - (manager_stats['bench_points'] / manager_stats['total_points'] * 100)
        best_bench_manager_idx = manager_stats['bench_efficiency'].idxmax() if 'bench_efficiency' in manager_stats.columns and not manager_stats['bench_efficiency'].isna().all() else None
        best_bench_manager = manager_stats.loc[best_bench_manager_idx, 'manager_name'] if best_bench_manager_idx is not None else "N/A"
        best_bench_efficiency = manager_stats['bench_efficiency'].max() if 'bench_efficiency' in manager_stats.columns else 0

    except (KeyError, ValueError):
        avg_transfers = max_transfers = total_transfer_cost = total_bench_points = 0
        most_active_name = least_active_name = most_efficient_name = best_bench_manager = "N/A"
        min_transfers = best_efficiency = best_bench_efficiency = 0

    return html.Div([
        html.H3("🔄 Transfers & Squad Management", className='section-title'),

        # Transfer Statistics Overview
        html.Div([
            html.H4("📊 Transfer Statistics Overview", style={'color': COLORS['text'], 'marginBottom': '16px'}),
            html.Div([
                html.P(f"📈 Average Transfers per Manager: {avg_transfers:.1f}",
                       style={'margin': '8px 0', 'fontSize': '1.1rem'}),
                html.P(f"🔄 Most Active: {most_active_name} ({max_transfers} transfers)",
                       style={'margin': '8px 0', 'fontSize': '1.1rem'}),
                html.P(f"🎯 Least Active: {least_active_name} ({min_transfers} transfers)",
                       style={'margin': '8px 0', 'fontSize': '1.1rem'}),
                html.P(f"💰 Total Points Lost to Transfers: {total_transfer_cost}",
                       style={'margin': '8px 0', 'fontSize': '1.1rem'}),
                html.P(f"🪑 Total Bench Points: {total_bench_points}",
                       style={'margin': '8px 0', 'fontSize': '1.1rem'}),
            ], style={'backgroundColor': COLORS['background'], 'padding': '20px', 'borderRadius': '12px'})
        ], style={'marginBottom': '32px'}),

        # Transfer Efficiency Analysis
        html.Div([
            html.H4("⚡ Transfer & Squad Efficiency", style={'color': COLORS['text'], 'marginBottom': '16px'}),
            html.Div([
                html.Div([
                    html.H5("🎯 Most Efficient Transfers", style={'color': COLORS['primary'], 'marginBottom': '12px'}),
                    html.P(f"Manager: {most_efficient_name}",
                           style={'margin': '4px 0', 'fontSize': '1rem', 'fontWeight': 'bold'}),
                    html.P(f"Efficiency: {best_efficiency:.1f} points per transfer",
                           style={'margin': '4px 0', 'fontSize': '0.9rem', 'color': COLORS['muted']}),
                    html.P("Strategy: Maximized points with minimal transfers",
                           style={'margin': '4px 0', 'fontSize': '0.85rem', 'fontStyle': 'italic'}),
                ], style={'backgroundColor': COLORS['background'], 'padding': '16px', 'borderRadius': '8px', 'width': '48%', 'display': 'inline-block'}),

                html.Div([
                    html.H5("🪑 Best Squad Management", style={'color': COLORS['secondary'], 'marginBottom': '12px'}),
                    html.P(f"Manager: {best_bench_manager}",
                           style={'margin': '4px 0', 'fontSize': '1rem', 'fontWeight': 'bold'}),
                    html.P(f"Efficiency: {best_bench_efficiency:.1f}% points utilized",
                           style={'margin': '4px 0', 'fontSize': '0.9rem', 'color': COLORS['muted']}),
                    html.P("Strategy: Minimized bench points wastage",
                           style={'margin': '4px 0', 'fontSize': '0.85rem', 'fontStyle': 'italic'}),
                ], style={'backgroundColor': COLORS['background'], 'padding': '16px', 'borderRadius': '8px', 'width': '48%', 'display': 'inline-block', 'marginLeft': '4%'}),
            ])
        ], style={'marginBottom': '32px'}),

        # Transfer Strategy Insights
        html.Div([
            html.H4("🧠 Transfer Strategy Insights", style={'color': COLORS['text'], 'marginBottom': '16px'}),
            html.Div([
                html.Div([
                    html.H5("📈 High Activity Strategy", style={'color': COLORS['warning'], 'marginBottom': '8px'}),
                    html.P(f"• {most_active_name} made {max_transfers} transfers",
                           style={'margin': '4px 0', 'fontSize': '0.9rem'}),
                    html.P(f"• Cost: {manager_stats.loc[most_active_idx, 'transfer_cost'] if most_active_idx is not None else 0} points",
                           style={'margin': '4px 0', 'fontSize': '0.9rem'}),
                    html.P("• Aggressive approach with frequent changes",
                           style={'margin': '4px 0', 'fontSize': '0.85rem', 'fontStyle': 'italic'}),
                ], style={'width': '48%', 'display': 'inline-block'}),

                html.Div([
                    html.H5("🎯 Conservative Strategy", style={'color': COLORS['success'], 'marginBottom': '8px'}),
                    html.P(f"• {least_active_name} made only {min_transfers} transfers",
                           style={'margin': '4px 0', 'fontSize': '0.9rem'}),
                    html.P(f"• Cost: {manager_stats.loc[least_active_idx, 'transfer_cost'] if least_active_idx is not None else 0} points",
                           style={'margin': '4px 0', 'fontSize': '0.9rem'}),
                    html.P("• Set-and-forget approach with minimal changes",
                           style={'margin': '4px 0', 'fontSize': '0.85rem', 'fontStyle': 'italic'}),
                ], style={'width': '48%', 'display': 'inline-block', 'paddingLeft': '4%'}),
            ], style={'backgroundColor': COLORS['background'], 'padding': '20px', 'borderRadius': '12px'})
        ], style={'marginBottom': '32px'}),

        # League Transfer Trends
        html.Div([
            html.H4("📊 League Transfer Trends", style={'color': COLORS['text'], 'marginBottom': '16px'}),
            html.Div([
                html.P(f"🎯 Transfer Range: {min_transfers} - {max_transfers} transfers",
                       style={'margin': '8px 0', 'fontSize': '1rem'}),
                html.P(f"💡 Strategy Diversity: {'High' if (max_transfers - min_transfers) > 20 else 'Moderate'} variation in approaches",
                       style={'margin': '8px 0', 'fontSize': '1rem'}),
                html.P(f"⚖️ League Style: {'Active' if avg_transfers > 15 else 'Conservative'} transfer market",
                       style={'margin': '8px 0', 'fontSize': '1rem'}),
                html.P(f"💰 Average Cost per Manager: {total_transfer_cost / len(manager_stats):.1f} points",
                       style={'margin': '8px 0', 'fontSize': '1rem'}),
            ], style={'backgroundColor': COLORS['background'], 'padding': '20px', 'borderRadius': '12px'})
        ]),
    ])

def create_manager_tab():
    """Individual Manager Analysis"""
    return html.Div([
        html.H3("👤 Individual Manager Analysis", className='section-title'),

        html.Div([
            html.Label("Select Manager:", style={'fontWeight': '600', 'marginBottom': '8px', 'display': 'block'}),
            dcc.Dropdown(
                id='manager-dropdown',
                options=[
                    {'label': row['entry_name'], 'value': row['entry']}  # Changed to show only team name
                    for _, row in analyzer.league_data.iterrows()
                ],
                value=analyzer.league_data.iloc[0]['entry'],
                style={'marginBottom': '24px', 'maxWidth': '400px'}
            ),
        ], style={'textAlign': 'center'}),

        html.Div(id='manager-analysis-content'),
    ])

def create_comparison_tab():
    """Manager Comparison Tab"""
    return html.Div([
        html.H3("⚖️ Manager Comparison", className='section-title'),

        # Manager selection
        html.Div([
            html.Div([
                html.Label("Select First Manager:", style={'fontWeight': '600', 'marginBottom': '8px', 'display': 'block'}),
                dcc.Dropdown(
                    id='manager1-dropdown',
                    options=[
                        {'label': row['entry_name'], 'value': row['entry']}
                        for _, row in analyzer.league_data.iterrows()
                    ],
                    value=analyzer.league_data.iloc[0]['entry'],
                    style={'marginBottom': '24px'}
                ),
            ], style={'width': '48%', 'display': 'inline-block'}),

            html.Div([
                html.Label("Select Second Manager:", style={'fontWeight': '600', 'marginBottom': '8px', 'display': 'block'}),
                dcc.Dropdown(
                    id='manager2-dropdown',
                    options=[
                        {'label': row['entry_name'], 'value': row['entry']}
                        for _, row in analyzer.league_data.iterrows()
                    ],
                    value=analyzer.league_data.iloc[1]['entry'] if len(analyzer.league_data) > 1 else analyzer.league_data.iloc[0]['entry'],
                    style={'marginBottom': '24px'}
                ),
            ], style={'width': '48%', 'display': 'inline-block', 'paddingLeft': '4%'}),
        ], style={'textAlign': 'center'}),

        html.Div(id='comparison-content'),
    ])

def create_season_story_tab():
    """Season Story with Star Wars narrative"""

    # Analyze position changes throughout the season
    def generate_star_wars_narrative():
        if not analyzer.manager_histories:
            return "The archives are incomplete, young Padawan. No season data found."

        # Get team names for narrative
        champion = analyzer.league_data.iloc[0]['entry_name']
        runner_up = analyzer.league_data.iloc[1]['entry_name'] if len(analyzer.league_data) > 1 else "Unknown"
        last_place = analyzer.league_data.iloc[-1]['entry_name']

        # Analyze different phases of the season
        early_leader = "Unknown"
        mid_season_leader = "Unknown"
        biggest_riser = "Unknown"
        biggest_faller = "Unknown"

        try:
            # Early season analysis (GW 1-10)
            early_gw_data = []
            mid_gw_data = []
            position_changes = {}

            for _, manager in analyzer.league_data.iterrows():
                history = analyzer.manager_histories.get(manager['entry'])
                if history is not None and not history.empty and 'points' in history.columns:
                    team_name = manager['entry_name']

                    # Early season points (first 10 GWs)
                    early_points = history.head(10)['points'].sum() if len(history) >= 10 else history['points'].sum()
                    early_gw_data.append((team_name, early_points))

                    # Mid season points (GW 11-25)
                    if len(history) >= 25:
                        mid_points = history.iloc[10:25]['points'].sum()
                        mid_gw_data.append((team_name, mid_points))

                    # Position tracking for rises/falls
                    if len(history) >= 20:
                        early_cumulative = history.head(10)['points'].sum()
                        late_cumulative = history.head(20)['points'].sum()
                        position_changes[team_name] = late_cumulative - early_cumulative

            # Determine leaders and movers
            if early_gw_data:
                early_leader = max(early_gw_data, key=lambda x: x[1])[0]
            if mid_gw_data:
                mid_season_leader = max(mid_gw_data, key=lambda x: x[1])[0]
            if position_changes:
                biggest_riser = max(position_changes.items(), key=lambda x: x[1])[0]
                biggest_faller = min(position_changes.items(), key=lambda x: x[1])[0]

        except Exception as e:
            print(f"Error in narrative analysis: {e}")
            pass

        # Generate dark side narrative
        narrative = f"""
        🌌 **Did you ever hear the tragedy of Darth Plagueis the wise?**

        **Season Review: The Dark Lord's Ascension**

        **THE PHANTOM MENACE (Early Season Deception)**

        In the early days of the season, when naive managers believed in hope and fair play,  Muscroft AFC strutted around like the overconfident Jedi Council, believing their early success made them untouchable. Little did they know that their arrogance would be their downfall, much like the Jedi who failed to see the Sith Lord right under their noses. Classic Jedi Hubris.

        **ATTACK OF THE CLONES (Mid-Season Manipulation)**

        As the season progressed, the true nature of {champion} began to reveal itself. Like Darth Sidious orchestrating the Clone Wars from both sides, FeelingSlotSlotSlot manipulated the transfer market with surgical precision. They watched as Muscroft AFC, drunk on early success, made increasingly desperate moves - each transfer a step closer to their inevitable fall from grace.

        Expected Toulouse, meanwhile, proved to be as useful as a battle droid with a malfunction. Their performance was so catastrophically poor that even the Trade Federation would have scrapped them for parts. Week after week, they demonstrated that having "Expected" in their name was the cruelest joke in the galaxy - the only thing expected was disappointment.

        Patatas Bravas found themselves in the role of a Clone Trooper - competent, reliable, but ultimately expendable. They followed orders, made sensible transfers, and maintained their position with the mechanical efficiency of someone who knew their place in the grand scheme of things.

        **REVENGE OF THE SITH (The Final Domination)**

        In the final third of the season, {champion} dropped all pretense of being anything other than a Sith Lord. Like Anakin's transformation into Darth Vader, their ascent was both inevitable and terrifying. They struck down their opponents not with lightsabers, but with perfectly timed captain choices and transfers that seemed to bend the very fabric of FPL reality to their will.

        Muscroft AFC's collapse was spectacular - a fall from grace that would make Anakin's turn to the dark side look like a minor career change. They went from potential champions to also-rans faster than you could say "Order 66." Their lead evaporated like moisture farmers on Tatooine, leaving nothing but regret and what-if scenarios.

        Expected Toulouse continued their reign of incompetence, proving that some things in the galaxy are as constant as the twin suns of Tatooine. Their performance was so consistently awful that it became almost admirable in its dedication to mediocrity.

        In the end, {champion} stood triumphant, having embraced the dark side completely. They had learned what the Jedi never could - that sometimes, to achieve true power, you must be willing to do whatever it takes. The galaxy belonged to the Sith, and the Sith was FeelingSlotSlotSlot.

        **"The dark side of the Force is a pathway to many abilities some consider to be... unnatural." - Emperor Palpatine**

        **May the dark side be with you, always.**
        """

        return narrative

    return html.Div([
        html.H3("📖 The Season Story", className='section-title'),

        html.Div([
            html.Div([
                dcc.Markdown(
                    generate_star_wars_narrative(),
                    style={
                        'fontSize': '1.1rem',
                        'lineHeight': '1.8',
                        'color': COLORS['text'],
                        'textAlign': 'justify'
                    }
                )
            ], style={
                'backgroundColor': COLORS['background'],
                'padding': '30px',
                'borderRadius': '12px',
                'border': f'2px solid {COLORS["primary"]}',
                'boxShadow': '0 4px 6px rgba(0, 0, 0, 0.1)'
            })
        ], style={'marginBottom': '32px'}),

        # Season highlights - Dark Side Edition
        html.Div([
            html.H4("⭐ The Dark Side Chronicles", style={'color': COLORS['text'], 'marginBottom': '16px'}),
            html.Div([
                html.Div([
                    html.H5("👑 The Dark Lord", style={'color': COLORS['danger'], 'marginBottom': '8px'}),
                    html.P(f"{analyzer.league_data.iloc[0]['entry_name']} - {analyzer.league_data.iloc[0]['total']:,} points"),
                    html.P("Like Emperor Palpatine, they orchestrated their rise to power with ruthless precision."),
                ], style={'width': '48%', 'display': 'inline-block', 'padding': '16px', 'backgroundColor': 'white', 'borderRadius': '8px', 'margin': '1%', 'border': f'2px solid {COLORS["danger"]}'}),

                html.Div([
                    html.H5("🤖 The Clone Trooper", style={'color': COLORS['muted'], 'marginBottom': '8px'}),
                    html.P(f"Patatas Bravas - {analyzer.league_data[analyzer.league_data['entry_name'] == 'Patatas Bravas']['total'].iloc[0] if len(analyzer.league_data[analyzer.league_data['entry_name'] == 'Patatas Bravas']) > 0 else 'Unknown':,} points"),
                    html.P("Reliable, competent, but ultimately following orders in the grand scheme."),
                ], style={'width': '48%', 'display': 'inline-block', 'padding': '16px', 'backgroundColor': 'white', 'borderRadius': '8px', 'margin': '1%'}),
            ]),

            html.Div([
                html.Div([
                    html.H5("💥 The Fallen Jedi", style={'color': COLORS['warning'], 'marginBottom': '8px'}),
                    html.P(f"Muscroft AFC - {analyzer.league_data[analyzer.league_data['entry_name'] == 'Muscroft AFC']['total'].iloc[0] if len(analyzer.league_data[analyzer.league_data['entry_name'] == 'Muscroft AFC']) > 0 else 'Unknown':,} points"),
                    html.P("Once promising, but their arrogance led to a spectacular fall from grace."),
                ], style={'width': '48%', 'display': 'inline-block', 'padding': '16px', 'backgroundColor': 'white', 'borderRadius': '8px', 'margin': '1%'}),

                html.Div([
                    html.H5("🤖 The Malfunctioning Droid", style={'color': COLORS['secondary'], 'marginBottom': '8px'}),
                    html.P(f"Expected Toulouse - {analyzer.league_data[analyzer.league_data['entry_name'] == 'Expected Toulouse']['total'].iloc[0] if len(analyzer.league_data[analyzer.league_data['entry_name'] == 'Expected Toulouse']) > 0 else 'Unknown':,} points"),
                    html.P("So consistently disappointing, even the Trade Federation would scrap them."),
                ], style={'width': '48%', 'display': 'inline-block', 'padding': '16px', 'backgroundColor': 'white', 'borderRadius': '8px', 'margin': '1%'}),
            ], style={'marginTop': '16px'})
        ], style={'backgroundColor': COLORS['background'], 'padding': '20px', 'borderRadius': '12px'})
    ])

def create_season_comparison_tab():
    """Season Comparison Tab"""

    # Load available seasons
    historical_summary = load_historical_seasons_summary()

    # Default to 2023 (last season) if available, otherwise 2022
    default_season = 2023 if historical_summary and 2023 in historical_summary['available_seasons'] else 2022

    return html.Div([
        html.H3("📅 Season Comparison Analytics", className='section-title'),

        # Season Selection Dropdown
        html.Div([
            html.H4("🔄 Select Season to Compare", style={'color': COLORS['text'], 'marginBottom': '16px'}),
            html.Div([
                html.Label("Compare current season with:", style={'fontWeight': '600', 'marginBottom': '8px', 'display': 'block'}),
                dcc.Dropdown(
                    id='season-comparison-dropdown',
                    options=[
                        {'label': f"{historical_summary['season_mapping'][str(year)]} Season", 'value': year}
                        for year in historical_summary['available_seasons']
                    ] if historical_summary else [
                        {'label': '2023-24 Season', 'value': 2023},
                        {'label': '2022-23 Season', 'value': 2022}
                    ],
                    value=default_season,
                    style={'marginBottom': '16px'}
                ),
            ], style={'backgroundColor': COLORS['background'], 'padding': '20px', 'borderRadius': '12px'})
        ], style={'marginBottom': '32px'}),

        # Comparison content (will be populated by callback)
        html.Div(id='season-comparison-content')
    ])

def calculate_position_changes(current_standings, historical_standings):
    """Calculate position changes between seasons"""
    position_changes = []

    # Create lookup for historical positions
    historical_positions = {}
    for i, manager in enumerate(historical_standings):
        historical_positions[manager['entry_name']] = i + 1

    # Calculate changes for current standings
    for i, manager in enumerate(current_standings):
        current_pos = i + 1
        team_name = manager['entry_name']
        historical_pos = historical_positions.get(team_name, None)

        if historical_pos:
            change = historical_pos - current_pos  # Positive = moved up, negative = moved down
            position_changes.append({
                'team_name': team_name,
                'current_pos': current_pos,
                'historical_pos': historical_pos,
                'change': change
            })

    return position_changes

def create_season_comparison_content(selected_season):
    """Create the actual comparison content for the selected season"""

    # Load selected season's data
    last_season_data = load_season_data(selected_season)

    # Calculate current season metrics for comparison framework
    current_season_metrics = {
        'champion_points': league_stats['champion_points'],
        'average_points': league_stats['average_points'],
        'points_range': league_stats['points_range'],
        'std_points': league_stats['std_points'],
        'total_managers': league_stats['total_managers'],
        'competition_level': 'Very Close' if league_stats['std_points'] < 100 else 'Spread Out'
    }

    # Use real previous season data
    previous_season_metrics = {
        'champion_points': last_season_data['champion_points'],
        'average_points': last_season_data['average_points'],
        'points_range': last_season_data['points_range'],
        'std_points': last_season_data['std_points'],
        'total_managers': last_season_data['total_managers'],
        'competition_level': last_season_data['competition_level'],
        'champion_name': last_season_data['champion_name']
    }

    # Calculate differences
    points_diff = current_season_metrics['champion_points'] - previous_season_metrics['champion_points']
    avg_diff = current_season_metrics['average_points'] - previous_season_metrics['average_points']
    range_diff = current_season_metrics['points_range'] - previous_season_metrics['points_range']

    # Check data quality and type
    is_simulated_data = last_season_data.get('season_id') == 'simulated' or last_season_data.get('season_name') == 'Default'
    is_real_historical_data = 'season_name' in last_season_data and last_season_data.get('season_name') not in ['Default', 'Simulated 2023-24']
    is_same_data = (
        current_season_metrics['champion_points'] == previous_season_metrics['champion_points'] and
        abs(current_season_metrics['average_points'] - previous_season_metrics['average_points']) < 1
    ) and not is_simulated_data and not is_real_historical_data

    return html.Div([
        # Current vs Previous Season Overview
        html.Div([
            html.H4("⚖️ Season-on-Season Comparison", style={'color': COLORS['text'], 'marginBottom': '16px'}),

            html.Div([
                # Current Season Column
                html.Div([
                    html.H5("🏆 Current Season (2024/25)", style={'color': COLORS['primary'], 'textAlign': 'center', 'marginBottom': '16px'}),
                    html.Div([
                        html.P(f"🥇 Champion: {league_stats['champion_name']}", style={'margin': '8px 0', 'fontSize': '1rem'}),
                        html.P(f"🎯 Winning Score: {current_season_metrics['champion_points']:,} points", style={'margin': '8px 0', 'fontSize': '1rem'}),
                        html.P(f"📊 League Average: {current_season_metrics['average_points']:.0f} points", style={'margin': '8px 0', 'fontSize': '1rem'}),
                        html.P(f"📈 Points Range: {current_season_metrics['points_range']:,} points", style={'margin': '8px 0', 'fontSize': '1rem'}),
                        html.P(f"🎲 Standard Deviation: {current_season_metrics['std_points']:.0f}", style={'margin': '8px 0', 'fontSize': '1rem'}),
                        html.P(f"⚔️ Competition: {current_season_metrics['competition_level']}", style={'margin': '8px 0', 'fontSize': '1rem'}),
                    ], style={'backgroundColor': 'white', 'padding': '16px', 'borderRadius': '8px'})
                ], style={'width': '45%', 'display': 'inline-block'}),

                # VS Indicator
                html.Div([
                    html.H3("VS", style={'color': COLORS['text'], 'textAlign': 'center', 'margin': '0', 'fontSize': '2rem'}),
                    html.P("📊", style={'textAlign': 'center', 'fontSize': '3rem', 'margin': '10px 0'})
                ], style={'width': '10%', 'display': 'inline-block', 'textAlign': 'center', 'verticalAlign': 'middle'}),

                # Previous Season Column
                html.Div([
                    html.H5(f"📅 Previous Season ({last_season_data.get('season_name', '2022/23')})", style={'color': COLORS['secondary'], 'textAlign': 'center', 'marginBottom': '16px'}),
                    html.Div([
                        html.P(f"🥇 Champion: {previous_season_metrics['champion_name']}", style={'margin': '8px 0', 'fontSize': '1rem'}),
                        html.P(f"🎯 Winning Score: {previous_season_metrics['champion_points']:,} points", style={'margin': '8px 0', 'fontSize': '1rem'}),
                        html.P(f"📊 League Average: {previous_season_metrics['average_points']:.0f} points", style={'margin': '8px 0', 'fontSize': '1rem'}),
                        html.P(f"📈 Points Range: {previous_season_metrics['points_range']:,} points", style={'margin': '8px 0', 'fontSize': '1rem'}),
                        html.P(f"🎲 Standard Deviation: {previous_season_metrics['std_points']:.0f}", style={'margin': '8px 0', 'fontSize': '1rem'}),
                        html.P(f"⚔️ Competition: {previous_season_metrics['competition_level']}", style={'margin': '8px 0', 'fontSize': '1rem'}),
                    ], style={'backgroundColor': 'white', 'padding': '16px', 'borderRadius': '8px'})
                ], style={'width': '45%', 'display': 'inline-block'}),
            ])
        ], style={'backgroundColor': COLORS['background'], 'padding': '20px', 'borderRadius': '12px', 'marginBottom': '32px'}),

        # Difference Analysis
        html.Div([
            html.H4("📈 Year-on-Year Changes", style={'color': COLORS['text'], 'marginBottom': '16px'}),
            html.Div([
                html.Div([
                    html.H5("🏆 Champion Performance", style={'color': COLORS['primary'], 'marginBottom': '8px'}),
                    html.P(f"Change: {points_diff:+,} points",
                           style={'margin': '4px 0', 'fontSize': '1.2rem', 'fontWeight': 'bold',
                                 'color': COLORS['success'] if points_diff > 0 else COLORS['danger']}),
                    html.P(f"{'📈 Higher scoring season' if points_diff > 0 else '📉 Lower scoring season'}",
                           style={'margin': '4px 0', 'fontSize': '0.9rem', 'fontStyle': 'italic'}),
                ], style={'width': '30%', 'display': 'inline-block', 'padding': '16px', 'backgroundColor': 'white', 'borderRadius': '8px', 'margin': '1.5%'}),

                html.Div([
                    html.H5("📊 League Average", style={'color': COLORS['secondary'], 'marginBottom': '8px'}),
                    html.P(f"Change: {avg_diff:+.0f} points",
                           style={'margin': '4px 0', 'fontSize': '1.2rem', 'fontWeight': 'bold',
                                 'color': COLORS['success'] if avg_diff > 0 else COLORS['danger']}),
                    html.P(f"{'🔥 Stronger league overall' if avg_diff > 0 else '❄️ Weaker league overall'}",
                           style={'margin': '4px 0', 'fontSize': '0.9rem', 'fontStyle': 'italic'}),
                ], style={'width': '30%', 'display': 'inline-block', 'padding': '16px', 'backgroundColor': 'white', 'borderRadius': '8px', 'margin': '1.5%'}),

                html.Div([
                    html.H5("⚔️ Competition Level", style={'color': COLORS['warning'], 'marginBottom': '8px'}),
                    html.P(f"Range: {range_diff:+,} points",
                           style={'margin': '4px 0', 'fontSize': '1.2rem', 'fontWeight': 'bold',
                                 'color': COLORS['danger'] if range_diff > 0 else COLORS['success']}),
                    html.P(f"{'📈 More spread out' if range_diff > 0 else '🎯 Closer competition'}",
                           style={'margin': '4px 0', 'fontSize': '0.9rem', 'fontStyle': 'italic'}),
                ], style={'width': '30%', 'display': 'inline-block', 'padding': '16px', 'backgroundColor': 'white', 'borderRadius': '8px', 'margin': '1.5%'}),
            ])
        ], style={'backgroundColor': COLORS['background'], 'padding': '20px', 'borderRadius': '12px', 'marginBottom': '32px'}),

        # League Position Changes (only show if we have real historical data)
        html.Div([
            html.H4("🔄 League Position Changes", style={'color': COLORS['text'], 'marginBottom': '16px'}),
            html.Div([
                html.P(f"📈 Position changes from {last_season_data.get('season_name', 'previous season')} to current season:",
                       style={'fontSize': '1.1rem', 'fontWeight': 'bold', 'marginBottom': '12px'}),
                html.Div(id='position-changes-list'),
                html.P("🎯 This shows how competitive and unpredictable FPL can be year-on-year!",
                       style={'fontSize': '1rem', 'fontStyle': 'italic', 'color': COLORS['primary'], 'marginTop': '16px', 'textAlign': 'center'})
            ], style={'backgroundColor': 'white', 'padding': '20px', 'borderRadius': '8px'})
        ], style={'backgroundColor': COLORS['background'], 'padding': '20px', 'borderRadius': '12px', 'marginBottom': '32px'}) if is_real_historical_data else html.Div(),

        # Data Source Information
        html.Div([
            html.H4("📊 Data Source Information", style={'color': COLORS['text'], 'marginBottom': '16px'}),
            html.Div([
                html.Div([
                    html.H5("✅ Real Historical Data" if is_real_historical_data else "🔍 Data Analysis",
                            style={'color': COLORS['success'] if is_real_historical_data else COLORS['warning'], 'marginBottom': '8px'}),
                    html.P(
                        f"Successfully retrieved real historical data from {last_season_data.get('season_name', 'previous season')} by querying individual team histories via the FPL API. This shows actual league performance from that season." if is_real_historical_data else
                        "After testing multiple season IDs, the FPL API returned identical data. Using simulated comparison data for demonstration." if is_simulated_data else
                        "The retrieved data appears to match the current season across all tested season IDs." if is_same_data else
                        f"Retrieved historical data from {last_season_data.get('season_name', 'previous season')}.",
                        style={'margin': '8px 0', 'fontSize': '1rem'}
                    ),
                    html.P(f"📅 Comparison includes {previous_season_metrics['total_managers']} managers.",
                           style={'margin': '8px 0', 'fontSize': '0.9rem', 'color': COLORS['muted']}),
                    html.P("🎯 Data fetched using individual team history queries as recommended in FPL API guides." if is_real_historical_data else
                           "💡 Real historical data now available using proper API methodology." if is_simulated_data else "",
                           style={'margin': '8px 0', 'fontSize': '0.85rem', 'fontStyle': 'italic', 'color': COLORS['muted']}),
                ], style={'width': '48%', 'display': 'inline-block', 'padding': '16px', 'backgroundColor': 'white', 'borderRadius': '8px', 'margin': '1%'}),

                html.Div([
                    html.H5("🔧 Technical Implementation", style={'color': COLORS['primary'], 'marginBottom': '8px'}),
                    html.Ul([
                        html.Li("📊 Real FPL API data integration"),
                        html.Li("🔄 Automated data fetching script"),
                        html.Li("📈 Statistical comparison framework"),
                        html.Li("🏆 Multi-season analysis capability"),
                    ], style={'color': COLORS['text'], 'lineHeight': '1.6', 'fontSize': '0.9rem'}),
                ], style={'width': '48%', 'display': 'inline-block', 'padding': '16px', 'backgroundColor': 'white', 'borderRadius': '8px', 'margin': '1%'}),
            ])
        ], style={'backgroundColor': COLORS['background'], 'padding': '20px', 'borderRadius': '12px'})
    ])

def create_surprise_tab():
    """Surprise tab with Darth Maul"""
    return html.Div([
        html.H3("🎁 Surprise!", className='section-title'),

        html.Div([
            html.Div([
                html.H2("You have been chosen by the Dark Side...",
                       style={'color': COLORS['danger'], 'textAlign': 'center', 'marginBottom': '20px', 'fontSize': '2rem'}),

                html.Div([
                    html.Img(
                        src="/assets/darth_maul.jpeg",  # Local Darth Maul image
                        style={
                            'width': '100%',
                            'maxWidth': '500px',
                            'height': 'auto',
                            'borderRadius': '12px',
                            'boxShadow': '0 8px 16px rgba(0, 0, 0, 0.3)'
                        }
                    )
                ], style={'textAlign': 'center', 'marginBottom': '20px'}),

                html.H3("\"At last we will reveal ourselves to the Jedi. At last we will have revenge.\"",
                       style={'color': COLORS['text'], 'textAlign': 'center', 'fontStyle': 'italic', 'marginBottom': '20px'}),

                html.P("- Darth Maul",
                       style={'color': COLORS['muted'], 'textAlign': 'center', 'fontSize': '1.2rem'}),

                html.Div([
                    html.P("🌟 May the Force be with your FPL team! 🌟",
                           style={'color': COLORS['primary'], 'textAlign': 'center', 'fontSize': '1.5rem', 'fontWeight': 'bold', 'marginTop': '30px'})
                ])

            ], style={
                'backgroundColor': COLORS['background'],
                'padding': '40px',
                'borderRadius': '12px',
                'border': f'3px solid {COLORS["danger"]}',
                'boxShadow': '0 4px 8px rgba(0, 0, 0, 0.1)',
                'textAlign': 'center'
            })
        ], style={'maxWidth': '800px', 'margin': '0 auto'})
    ])

# Season comparison callback
@app.callback(
    [Output('season-comparison-content', 'children'),
     Output('position-changes-list', 'children')],
    Input('season-comparison-dropdown', 'value')
)
def update_season_comparison(selected_season):
    if not selected_season:
        return html.Div("Please select a season to compare"), html.Div()

    # Get the comparison content
    comparison_content = create_season_comparison_content(selected_season)

    # Calculate position changes
    last_season_data = load_season_data(selected_season)
    position_changes_content = html.Div()

    if 'raw_standings' in last_season_data and last_season_data['raw_standings']:
        try:
            # Get current standings from analyzer
            current_standings = analyzer.league_data.to_dict('records')
            historical_standings = last_season_data['raw_standings']

            position_changes = calculate_position_changes(current_standings, historical_standings)

            # Create position changes list
            position_items = []
            for change_data in position_changes:
                team = change_data['team_name']
                current = change_data['current_pos']
                historical = change_data['historical_pos']
                change = change_data['change']

                if change > 0:
                    emoji = "🏆" if current == 1 else "📈"
                    description = f"(+{change} positions!) - {'Championship winning rise' if current == 1 else 'Great improvement'}"
                    color = COLORS['success']
                elif change < 0:
                    emoji = "📉"
                    description = f"({change} positions) - {'Defending champion fall' if historical == 1 else 'Dropped down'}"
                    color = COLORS['danger']
                else:
                    emoji = "📊"
                    description = "(Consistent performer)"
                    color = COLORS['text']

                position_items.append(
                    html.Li(f"{emoji} {team}: {historical} → {current} {description}",
                           style={'color': color, 'margin': '4px 0'})
                )

            position_changes_content = html.Ul(position_items,
                                             style={'color': COLORS['text'], 'lineHeight': '1.8', 'fontSize': '1rem'})

        except Exception as e:
            print(f"Error calculating position changes: {e}")
            position_changes_content = html.P("Position changes data not available for this season.")

    return comparison_content, position_changes_content

# Manager analysis callback
@app.callback(
    Output('manager-analysis-content', 'children'),
    Input('manager-dropdown', 'value')
)
def update_manager_analysis(selected_entry):
    if not selected_entry:
        return html.Div("Please select a manager")

    manager_data = analyzer.league_data[analyzer.league_data['entry'] == selected_entry].iloc[0]
    history = analyzer.manager_histories.get(selected_entry)

    if history is None or history.empty:
        return html.Div("No history data available for this manager")

    # Calculate statistics
    if 'points' in history.columns:
        total_points = history['points'].sum()
        avg_points = history['points'].mean()
        best_gw = history.loc[history['points'].idxmax()]
        worst_gw = history.loc[history['points'].idxmin()]
        consistency = history['points'].std()
    else:
        total_points = avg_points = consistency = 0
        best_gw = worst_gw = {'event': 0, 'points': 0}

    total_transfers = history['event_transfers'].sum() if 'event_transfers' in history.columns else 0
    transfer_cost = history['event_transfers_cost'].sum() if 'event_transfers_cost' in history.columns else 0
    bench_points = history['points_on_bench'].sum() if 'points_on_bench' in history.columns else 0
    recent_form = history.tail(5)['points'].mean() if len(history) >= 5 and 'points' in history.columns else avg_points

    # Create enhanced performance chart
    if 'points' in history.columns and 'event' in history.columns:
        fig = make_subplots(
            rows=2, cols=1,
            subplot_titles=('Individual Gameweek Performance', 'Cumulative Points Progression'),
            vertical_spacing=0.1
        )

        # Individual gameweek points
        fig.add_trace(
            go.Scatter(
                x=history['event'],
                y=history['points'],
                mode='lines+markers',
                name='GW Points',
                line=dict(color=COLORS['secondary'], width=3),
                marker=dict(size=6),
                fill='tonexty'
            ),
            row=1, col=1
        )

        # Add average line
        fig.add_hline(
            y=avg_points,
            line_dash="dash",
            line_color=COLORS['danger'],
            annotation_text=f"Season Average: {avg_points:.1f}",
            row=1, col=1
        )

        # Cumulative points
        cumulative = history['points'].cumsum()
        fig.add_trace(
            go.Scatter(
                x=history['event'],
                y=cumulative,
                mode='lines+markers',
                name='Cumulative',
                line=dict(color=COLORS['primary'], width=3),
                marker=dict(size=6)
            ),
            row=2, col=1
        )

        fig.update_layout(
            title=dict(
                text=f"Detailed Performance Analysis - {manager_data['entry_name']}",  # Changed to team name
                font=dict(size=20, family='Inter', color=COLORS['text']),
                x=0.5
            ),
            height=700,
            paper_bgcolor='white',
            plot_bgcolor='white',
            showlegend=False
        )

        fig.update_xaxes(title_text="Gameweek", gridcolor='#e2e8f0', showgrid=True)
        fig.update_yaxes(title_text="Points", gridcolor='#e2e8f0', showgrid=True, row=1, col=1)
        fig.update_yaxes(title_text="Cumulative Points", gridcolor='#e2e8f0', showgrid=True, row=2, col=1)
    else:
        fig = go.Figure()

    return html.Div([
        # Enhanced stats grid
        html.Div([
            html.Div([
                html.Div("📊", className='award-icon'),
                html.Div(f"{manager_data['rank']}", className='metric-value', style={'fontSize': '2rem'}),
                html.Div("Final Position", className='metric-label'),
            ], className='stat-card'),

            html.Div([
                html.Div("🎯", className='award-icon'),
                html.Div(f"{total_points:,}", className='metric-value', style={'fontSize': '2rem'}),
                html.Div("Total Points", className='metric-label'),
            ], className='stat-card'),

            html.Div([
                html.Div("📈", className='award-icon'),
                html.Div(f"{avg_points:.1f}", className='metric-value', style={'fontSize': '2rem'}),
                html.Div("Average per GW", className='metric-label'),
            ], className='stat-card'),

            html.Div([
                html.Div("🎲", className='award-icon'),
                html.Div(f"{consistency:.1f}", className='metric-value', style={'fontSize': '2rem'}),
                html.Div("Consistency", className='metric-label'),
            ], className='stat-card'),

        ], style={'display': 'grid', 'gridTemplateColumns': 'repeat(auto-fit, minmax(200px, 1fr))',
                 'gap': '20px', 'marginBottom': '32px'}),

        # Performance chart
        dcc.Graph(figure=fig),

        # Detailed insights
        html.Div([
            html.H4("🔍 Detailed Performance Insights", style={'color': COLORS['text'], 'marginBottom': '16px'}),
            html.Div([
                html.Div([
                    html.P(f"🏆 Best Gameweek: GW{best_gw.get('event', 'N/A')} with {best_gw.get('points', 0)} points",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P(f"📉 Worst Gameweek: GW{worst_gw.get('event', 'N/A')} with {worst_gw.get('points', 0)} points",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P(f"🔄 Total Transfers: {total_transfers} (Cost: {transfer_cost} points)",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                ], style={'width': '48%', 'display': 'inline-block'}),

                html.Div([
                    html.P(f"🪑 Bench Points: {bench_points} points left unused",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P(f"🔥 Recent Form: {recent_form:.1f} average (last 5 GWs)",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P(f"📊 vs League Average: {total_points - league_stats['average_points']:+.0f} points",
                           style={'margin': '8px 0', 'fontSize': '1rem',
                                 'color': COLORS['success'] if total_points > league_stats['average_points'] else COLORS['danger']}),
                ], style={'width': '48%', 'display': 'inline-block', 'paddingLeft': '4%'}),
            ], style={'backgroundColor': COLORS['background'], 'padding': '20px', 'borderRadius': '12px'})
        ], style={'marginTop': '24px'})
    ])

# Manager comparison callback
@app.callback(
    Output('comparison-content', 'children'),
    [Input('manager1-dropdown', 'value'),
     Input('manager2-dropdown', 'value')]
)
def update_comparison(manager1_entry, manager2_entry):
    if not manager1_entry or not manager2_entry:
        return html.Div("Please select both managers")

    if manager1_entry == manager2_entry:
        return html.Div("Please select different managers for comparison")

    # Get manager data
    manager1_data = analyzer.league_data[analyzer.league_data['entry'] == manager1_entry].iloc[0]
    manager2_data = analyzer.league_data[analyzer.league_data['entry'] == manager2_entry].iloc[0]

    history1 = analyzer.manager_histories.get(manager1_entry)
    history2 = analyzer.manager_histories.get(manager2_entry)

    if history1 is None or history2 is None or history1.empty or history2.empty:
        return html.Div("History data not available for one or both managers")

    # Calculate comparison statistics
    def get_manager_stats(history):
        if 'points' not in history.columns:
            return {
                'total_points': 0, 'avg_points': 0, 'consistency': 0,
                'best_gw': 0, 'worst_gw': 0, 'total_transfers': 0,
                'transfer_cost': 0, 'bench_points': 0, 'recent_form': 0
            }

        total_points = history['points'].sum()
        avg_points = history['points'].mean()
        consistency = history['points'].std()
        best_gw = history['points'].max()
        worst_gw = history['points'].min()
        total_transfers = history['event_transfers'].sum() if 'event_transfers' in history.columns else 0
        transfer_cost = history['event_transfers_cost'].sum() if 'event_transfers_cost' in history.columns else 0
        bench_points = history['points_on_bench'].sum() if 'points_on_bench' in history.columns else 0
        recent_form = history.tail(5)['points'].mean() if len(history) >= 5 else avg_points

        return {
            'total_points': total_points, 'avg_points': avg_points, 'consistency': consistency,
            'best_gw': best_gw, 'worst_gw': worst_gw, 'total_transfers': total_transfers,
            'transfer_cost': transfer_cost, 'bench_points': bench_points, 'recent_form': recent_form
        }

    stats1 = get_manager_stats(history1)
    stats2 = get_manager_stats(history2)

    # Create comparison chart
    if 'points' in history1.columns and 'points' in history2.columns:
        fig = go.Figure()

        # Manager 1 cumulative points
        cumulative1 = history1['points'].cumsum()
        fig.add_trace(go.Scatter(
            x=history1['event'],
            y=cumulative1,
            mode='lines+markers',
            name=manager1_data['entry_name'],
            line=dict(width=3, color=COLORS['primary']),
            marker=dict(size=6)
        ))

        # Manager 2 cumulative points
        cumulative2 = history2['points'].cumsum()
        fig.add_trace(go.Scatter(
            x=history2['event'],
            y=cumulative2,
            mode='lines+markers',
            name=manager2_data['entry_name'],
            line=dict(width=3, color=COLORS['secondary']),
            marker=dict(size=6)
        ))

        fig.update_layout(
            title=dict(
                text=f"Head-to-Head Comparison: {manager1_data['entry_name']} vs {manager2_data['entry_name']}",
                font=dict(size=20, family='Inter', color=COLORS['text']),
                x=0.5
            ),
            xaxis=dict(title=dict(text="Gameweek", font=dict(family='Inter')), gridcolor='#e2e8f0', showgrid=True),
            yaxis=dict(title=dict(text="Cumulative Points", font=dict(family='Inter')), gridcolor='#e2e8f0', showgrid=True),
            height=500,
            paper_bgcolor='white',
            plot_bgcolor='white',
            legend=dict(orientation="h", yanchor="bottom", y=1.02, xanchor="center", x=0.5)
        )
    else:
        fig = go.Figure()

    return html.Div([
        # Comparison chart
        dcc.Graph(figure=fig),

        # Head-to-head statistics
        html.Div([
            html.H4("📊 Head-to-Head Statistics", style={'color': COLORS['text'], 'marginBottom': '24px', 'textAlign': 'center'}),

            html.Div([
                # Manager 1 column
                html.Div([
                    html.H5(manager1_data['entry_name'], style={'color': COLORS['primary'], 'textAlign': 'center', 'marginBottom': '16px'}),
                    html.Div([
                        html.P(f"🏆 Final Position: {manager1_data['rank']}", style={'margin': '8px 0'}),
                        html.P(f"🎯 Total Points: {stats1['total_points']:,}", style={'margin': '8px 0'}),
                        html.P(f"📈 Average per GW: {stats1['avg_points']:.1f}", style={'margin': '8px 0'}),
                        html.P(f"🎲 Consistency: {stats1['consistency']:.1f}", style={'margin': '8px 0'}),
                        html.P(f"🏅 Best GW: {stats1['best_gw']}", style={'margin': '8px 0'}),
                        html.P(f"📉 Worst GW: {stats1['worst_gw']}", style={'margin': '8px 0'}),
                        html.P(f"🔄 Transfers: {stats1['total_transfers']}", style={'margin': '8px 0'}),
                        html.P(f"💰 Transfer Cost: {stats1['transfer_cost']}", style={'margin': '8px 0'}),
                        html.P(f"🪑 Bench Points: {stats1['bench_points']}", style={'margin': '8px 0'}),
                        html.P(f"🔥 Recent Form: {stats1['recent_form']:.1f}", style={'margin': '8px 0'}),
                    ], style={'backgroundColor': COLORS['background'], 'padding': '20px', 'borderRadius': '12px'})
                ], style={'width': '45%', 'display': 'inline-block'}),

                # VS indicator
                html.Div([
                    html.H3("VS", style={'color': COLORS['text'], 'textAlign': 'center', 'margin': '0'})
                ], style={'width': '10%', 'display': 'inline-block', 'textAlign': 'center', 'verticalAlign': 'middle'}),

                # Manager 2 column
                html.Div([
                    html.H5(manager2_data['entry_name'], style={'color': COLORS['secondary'], 'textAlign': 'center', 'marginBottom': '16px'}),
                    html.Div([
                        html.P(f"🏆 Final Position: {manager2_data['rank']}", style={'margin': '8px 0'}),
                        html.P(f"🎯 Total Points: {stats2['total_points']:,}", style={'margin': '8px 0'}),
                        html.P(f"📈 Average per GW: {stats2['avg_points']:.1f}", style={'margin': '8px 0'}),
                        html.P(f"🎲 Consistency: {stats2['consistency']:.1f}", style={'margin': '8px 0'}),
                        html.P(f"🏅 Best GW: {stats2['best_gw']}", style={'margin': '8px 0'}),
                        html.P(f"📉 Worst GW: {stats2['worst_gw']}", style={'margin': '8px 0'}),
                        html.P(f"🔄 Transfers: {stats2['total_transfers']}", style={'margin': '8px 0'}),
                        html.P(f"💰 Transfer Cost: {stats2['transfer_cost']}", style={'margin': '8px 0'}),
                        html.P(f"🪑 Bench Points: {stats2['bench_points']}", style={'margin': '8px 0'}),
                        html.P(f"🔥 Recent Form: {stats2['recent_form']:.1f}", style={'margin': '8px 0'}),
                    ], style={'backgroundColor': COLORS['background'], 'padding': '20px', 'borderRadius': '12px'})
                ], style={'width': '45%', 'display': 'inline-block'}),
            ]),

            # Winner analysis
            html.Div([
                html.H4("🏆 Comparison Summary", style={'color': COLORS['text'], 'marginBottom': '16px', 'textAlign': 'center'}),
                html.Div([
                    html.P(f"🎯 Points Winner: {manager1_data['entry_name'] if stats1['total_points'] > stats2['total_points'] else manager2_data['entry_name']} ({max(stats1['total_points'], stats2['total_points']):,} vs {min(stats1['total_points'], stats2['total_points']):,})",
                           style={'margin': '8px 0', 'fontSize': '1.1rem', 'fontWeight': 'bold'}),
                    html.P(f"📈 More Consistent: {manager1_data['entry_name'] if stats1['consistency'] < stats2['consistency'] else manager2_data['entry_name']} (lower std dev)",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P(f"🔄 More Active: {manager1_data['entry_name'] if stats1['total_transfers'] > stats2['total_transfers'] else manager2_data['entry_name']} ({max(stats1['total_transfers'], stats2['total_transfers'])} transfers)",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                    html.P(f"🔥 Better Form: {manager1_data['entry_name'] if stats1['recent_form'] > stats2['recent_form'] else manager2_data['entry_name']} ({max(stats1['recent_form'], stats2['recent_form']):.1f} avg)",
                           style={'margin': '8px 0', 'fontSize': '1rem'}),
                ], style={'backgroundColor': COLORS['background'], 'padding': '20px', 'borderRadius': '12px', 'textAlign': 'center'})
            ], style={'marginTop': '24px'})
        ], style={'marginTop': '24px'})
    ])

if __name__ == '__main__':
    print("🌐 Starting Comprehensive FPL Dashboard...")
    print("📊 Open your browser and go to: http://127.0.0.1:8050")
    print("🎨 Features: Modern UI, Comprehensive Analysis, Awards System")
    app.run(debug=True, host='127.0.0.1', port=8050)
