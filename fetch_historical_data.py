"""
Proper Historical FPL Data Fetcher
Based on: https://medium.com/@frenzelts/fantasy-premier-league-api-endpoints-a-detailed-guide-acbd5598eb19

This script fetches individual team histories for specific seasons and compiles league standings manually.
"""

import requests
import json
import time
from typing import Dict, List, Optional

# Configuration
LEAGUE_ID = 623787
BASE_URL = "https://fantasy.premierleague.com/api"

# Season mapping - these are the actual FPL season IDs
SEASON_MAPPING = {
    2023: "2023-24",  # Current season
    2022: "2022-23",  # Previous season
    2021: "2021-22",
    2020: "2020-21",
    2019: "2019-20"
}

def get_current_league_members():
    """Get current league members to know which teams to query historically"""
    print("📊 Fetching current league members...")
    
    try:
        url = f"{BASE_URL}/leagues-classic/{LEAGUE_ID}/standings/"
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            members = []
            
            for manager in data["standings"]["results"]:
                members.append({
                    'entry_id': manager['entry'],
                    'entry_name': manager['entry_name'],
                    'player_name': manager['player_name'],
                    'current_total': manager['total']
                })
            
            print(f"✅ Found {len(members)} league members")
            return members
        else:
            print(f"❌ Failed to fetch league data: {response.status_code}")
            return []
            
    except Exception as e:
        print(f"❌ Error fetching league members: {e}")
        return []

def get_team_season_history(entry_id: int, target_season: int) -> Optional[Dict]:
    """
    Get a team's history for a specific season
    Uses the /api/entry/{team-id}/history/ endpoint
    """
    print(f"   📈 Fetching history for team {entry_id}...")
    
    try:
        url = f"{BASE_URL}/entry/{entry_id}/history/"
        response = requests.get(url)
        
        if response.status_code == 200:
            data = response.json()
            
            # Look through past seasons for the target season
            past_seasons = data.get("past", [])
            
            for season_data in past_seasons:
                # The season_name format is like "2022/23"
                season_name = season_data.get("season_name", "")
                
                # Convert season name to year (e.g., "2022/23" -> 2022)
                if "/" in season_name:
                    season_year = int(season_name.split("/")[0])
                    
                    if season_year == target_season:
                        print(f"      ✅ Found {SEASON_MAPPING.get(target_season, f'{target_season}-{target_season+1}')} data")
                        return {
                            'entry_id': entry_id,
                            'season_name': season_name,
                            'total_points': season_data.get("total_points", 0),
                            'rank': season_data.get("rank", 999999),
                            'season_data': season_data
                        }
            
            print(f"      ⚠️ No data found for {SEASON_MAPPING.get(target_season, f'{target_season}-{target_season+1}')}")
            return None
            
        else:
            print(f"      ❌ Failed to fetch history: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"      ❌ Error fetching team history: {e}")
        return None

def compile_historical_league_standings(target_season: int) -> Optional[List[Dict]]:
    """
    Compile historical league standings by querying each team individually
    """
    print(f"\n🔍 Compiling historical standings for {SEASON_MAPPING.get(target_season, f'{target_season}-{target_season+1}')}...")
    
    # Get current league members
    current_members = get_current_league_members()
    if not current_members:
        return None
    
    historical_standings = []
    
    # Query each team's history
    for member in current_members:
        entry_id = member['entry_id']
        entry_name = member['entry_name']
        player_name = member['player_name']
        
        # Get this team's historical data
        season_history = get_team_season_history(entry_id, target_season)
        
        if season_history:
            historical_standings.append({
                'entry': entry_id,
                'entry_name': entry_name,
                'player_name': player_name,
                'total': season_history['total_points'],
                'rank': len(historical_standings) + 1,  # Will be recalculated
                'season_name': season_history['season_name'],
                'historical_rank': season_history['rank'],  # Their overall FPL rank that season
                'season_data': season_history['season_data']
            })
        else:
            print(f"      ⚠️ {entry_name} has no data for {SEASON_MAPPING.get(target_season)}")
        
        time.sleep(0.5)  # Be polite to the API
    
    # Sort by total points (descending) and assign league ranks
    historical_standings.sort(key=lambda x: x['total'], reverse=True)
    
    for i, standing in enumerate(historical_standings):
        standing['rank'] = i + 1
    
    print(f"✅ Compiled standings for {len(historical_standings)} teams")
    return historical_standings if historical_standings else None

def calculate_historical_stats(standings: List[Dict]) -> Dict:
    """Calculate statistics from historical standings"""
    if not standings:
        return {}
    
    # Extract key metrics
    champion = standings[0]  # First place
    total_managers = len(standings)
    
    # Calculate points statistics
    points = [manager['total'] for manager in standings]
    champion_points = max(points)
    average_points = sum(points) / len(points)
    points_range = max(points) - min(points)
    
    # Calculate standard deviation
    variance = sum((p - average_points) ** 2 for p in points) / len(points)
    std_points = variance ** 0.5
    
    return {
        'champion_name': champion['entry_name'],
        'champion_points': champion_points,
        'average_points': average_points,
        'points_range': points_range,
        'std_points': std_points,
        'total_managers': total_managers,
        'competition_level': 'Very Close' if std_points < 100 else 'Spread Out',
        'season_name': champion.get('season_name', 'Unknown'),
        'raw_standings': standings
    }

def fetch_multiple_seasons():
    """Fetch data for multiple seasons and save them separately"""
    print("🚀 Starting comprehensive historical FPL data collection...")

    seasons_to_fetch = [2023, 2022, 2021, 2020]  # Most recent seasons first
    successful_seasons = {}

    for target_season in seasons_to_fetch:
        print(f"\n📅 Processing {SEASON_MAPPING.get(target_season, f'{target_season}-{target_season+1}')}...")

        historical_standings = compile_historical_league_standings(target_season)

        if historical_standings and len(historical_standings) > 0:
            # Calculate statistics
            stats = calculate_historical_stats(historical_standings)
            stats['season_year'] = target_season

            # Save data with season-specific filenames
            output_file = f"historical_season_{target_season}_standings.json"
            with open(output_file, "w") as f:
                json.dump(historical_standings, f, indent=2)

            stats_file = f"historical_season_{target_season}_stats.json"
            with open(stats_file, "w") as f:
                json.dump(stats, f, indent=2)

            successful_seasons[target_season] = {
                'stats': stats,
                'standings_file': output_file,
                'stats_file': stats_file
            }

            print(f"   ✅ {stats['champion_name']} ({stats['champion_points']:,} points)")

        else:
            print(f"   ❌ No data found for {SEASON_MAPPING.get(target_season)}")

    # Create a summary file with all available seasons
    summary = {
        'available_seasons': list(successful_seasons.keys()),
        'season_mapping': {year: SEASON_MAPPING.get(year, f'{year}-{year+1}') for year in successful_seasons.keys()},
        'seasons_data': {year: data['stats'] for year, data in successful_seasons.items()}
    }

    with open("historical_seasons_summary.json", "w") as f:
        json.dump(summary, f, indent=2)

    print(f"\n🎉 Historical data collection complete!")
    print(f"   📊 Successfully fetched {len(successful_seasons)} seasons")
    print(f"   📄 Summary saved to: historical_seasons_summary.json")

    return successful_seasons

def main():
    """Main function to fetch historical data"""
    return fetch_multiple_seasons()

if __name__ == "__main__":
    main()
