import requests
import json
import time

LEAGUE_ID = 623787
BASE_URL = "https://fantasy.premierleague.com/api"
OUTPUT_FILE = "last_season_standings.json"

# Try different season IDs - FPL seasons are numbered differently
SEASON_IDS_TO_TRY = [
    (8, "2022-23"),
    (9, "2023-24"),
    (10, "2024-25"),
    (7, "2021-22"),
    (6, "2020-21")
]

def fetch_last_season_standings(league_id, season_id):
    """Fetch last season's standings data"""
    all_results = []
    page = 1

    while True:
        url = f"{BASE_URL}/leagues-classic/{league_id}/standings/?ls-history={season_id}&page_standings={page}"
        print(f"📊 Fetching page {page} from last season...")
        
        try:
            res = requests.get(url)
            if res.status_code != 200:
                print(f"❌ Error fetching page {page}: {res.status_code}")
                break
                
            data = res.json()
            results = data["standings"]["results"]
            all_results.extend(results)

            if not data["standings"]["has_next"]:
                break
                
            page += 1
            time.sleep(1)  # Be polite to the server
            
        except Exception as e:
            print(f"❌ Error fetching data: {e}")
            break

    return all_results

def calculate_last_season_stats(standings):
    """Calculate statistics from last season's standings"""
    if not standings:
        return None
    
    # Extract key metrics
    champion = standings[0]  # First place
    total_managers = len(standings)
    
    # Calculate points statistics
    points = [manager['total'] for manager in standings]
    champion_points = max(points)
    average_points = sum(points) / len(points)
    points_range = max(points) - min(points)
    
    # Calculate standard deviation
    variance = sum((p - average_points) ** 2 for p in points) / len(points)
    std_points = variance ** 0.5
    
    return {
        'champion_name': champion['entry_name'],
        'champion_points': champion_points,
        'average_points': average_points,
        'points_range': points_range,
        'std_points': std_points,
        'total_managers': total_managers,
        'competition_level': 'Very Close' if std_points < 100 else 'Spread Out',
        'raw_standings': standings
    }

def try_different_seasons():
    """Try different season IDs to find historical data"""
    print("🚀 Searching for historical FPL data across different seasons...")

    # First, get current season data for comparison
    current_url = f"{BASE_URL}/leagues-classic/{LEAGUE_ID}/standings/"
    try:
        current_res = requests.get(current_url)
        if current_res.status_code == 200:
            current_data = current_res.json()
            current_standings = current_data["standings"]["results"]
            current_champion_points = current_standings[0]["total"] if current_standings else 0
            print(f"📊 Current season champion: {current_champion_points:,} points")
        else:
            current_champion_points = 0
    except:
        current_champion_points = 0

    # Try different season IDs
    for season_id, season_name in SEASON_IDS_TO_TRY:
        print(f"\n🔍 Trying Season ID {season_id} ({season_name})...")
        standings = fetch_last_season_standings(LEAGUE_ID, season_id)

        if standings:
            champion_points = standings[0]["total"]
            print(f"   📈 Champion points: {champion_points:,}")

            # Check if this is different from current season
            if abs(champion_points - current_champion_points) > 10:  # Allow small differences
                print(f"   ✅ Found different data! Using Season ID {season_id}")
                return standings, season_id, season_name
            else:
                print(f"   ⚠️ Data appears identical to current season")
        else:
            print(f"   ❌ No data found for Season ID {season_id}")

    print(f"\n⚠️ Could not find historical data different from current season")
    print(f"📝 This could mean:")
    print(f"   • League is new and doesn't have historical data")
    print(f"   • Season IDs need adjustment")
    print(f"   • API doesn't provide historical data for this league")

    return None, None, None

def main():
    """Main function to fetch and save last season data"""
    standings, season_id, season_name = try_different_seasons()

    if standings:
        # Calculate statistics
        stats = calculate_last_season_stats(standings)
        stats['season_id'] = season_id
        stats['season_name'] = season_name

        # Save raw standings
        with open(OUTPUT_FILE, "w") as f:
            json.dump(standings, f, indent=2)

        # Save processed statistics
        stats_file = "last_season_stats.json"
        with open(stats_file, "w") as f:
            json.dump(stats, f, indent=2)

        print(f"\n✅ Saved {len(standings)} managers to {OUTPUT_FILE}")
        print(f"✅ Saved statistics to {stats_file}")
        print(f"🏆 Historical champion ({season_name}): {stats['champion_name']} ({stats['champion_points']:,} points)")
        print(f"📊 League average: {stats['average_points']:.0f} points")

    else:
        print("\n❌ No historical data found - creating simulated comparison data")
        # Create simulated data for demonstration
        simulated_stats = {
            'champion_name': 'Historical Champion',
            'champion_points': 2250,
            'average_points': 2100,
            'points_range': 180,
            'std_points': 85,
            'total_managers': 4,
            'competition_level': 'Very Close',
            'season_id': 'simulated',
            'season_name': 'Simulated 2023-24'
        }

        with open("last_season_stats.json", "w") as f:
            json.dump(simulated_stats, f, indent=2)

        print("✅ Created simulated comparison data for demonstration")

if __name__ == "__main__":
    main()
