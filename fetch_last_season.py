import requests
import json
import time

LEAGUE_ID = 623787
SEASON_ID = 9  # 2023–24 season
BASE_URL = "https://fantasy.premierleague.com/api"
OUTPUT_FILE = "last_season_standings.json"

def fetch_last_season_standings(league_id, season_id):
    """Fetch last season's standings data"""
    all_results = []
    page = 1

    while True:
        url = f"{BASE_URL}/leagues-classic/{league_id}/standings/?ls-history={season_id}&page_standings={page}"
        print(f"📊 Fetching page {page} from last season...")
        
        try:
            res = requests.get(url)
            if res.status_code != 200:
                print(f"❌ Error fetching page {page}: {res.status_code}")
                break
                
            data = res.json()
            results = data["standings"]["results"]
            all_results.extend(results)

            if not data["standings"]["has_next"]:
                break
                
            page += 1
            time.sleep(1)  # Be polite to the server
            
        except Exception as e:
            print(f"❌ Error fetching data: {e}")
            break

    return all_results

def calculate_last_season_stats(standings):
    """Calculate statistics from last season's standings"""
    if not standings:
        return None
    
    # Extract key metrics
    champion = standings[0]  # First place
    total_managers = len(standings)
    
    # Calculate points statistics
    points = [manager['total'] for manager in standings]
    champion_points = max(points)
    average_points = sum(points) / len(points)
    points_range = max(points) - min(points)
    
    # Calculate standard deviation
    variance = sum((p - average_points) ** 2 for p in points) / len(points)
    std_points = variance ** 0.5
    
    return {
        'champion_name': champion['entry_name'],
        'champion_points': champion_points,
        'average_points': average_points,
        'points_range': points_range,
        'std_points': std_points,
        'total_managers': total_managers,
        'competition_level': 'Very Close' if std_points < 100 else 'Spread Out',
        'raw_standings': standings
    }

def main():
    """Main function to fetch and save last season data"""
    print("🚀 Fetching last season's FPL data...")
    standings = fetch_last_season_standings(LEAGUE_ID, SEASON_ID)
    
    if standings:
        # Calculate statistics
        stats = calculate_last_season_stats(standings)
        
        # Save raw standings
        with open(OUTPUT_FILE, "w") as f:
            json.dump(standings, f, indent=2)
        
        # Save processed statistics
        stats_file = "last_season_stats.json"
        with open(stats_file, "w") as f:
            json.dump(stats, f, indent=2)
        
        print(f"✅ Saved {len(standings)} managers to {OUTPUT_FILE}")
        print(f"✅ Saved statistics to {stats_file}")
        print(f"🏆 Last season champion: {stats['champion_name']} ({stats['champion_points']:,} points)")
        print(f"📊 League average: {stats['average_points']:.0f} points")
        
    else:
        print("❌ No data retrieved")

if __name__ == "__main__":
    main()
